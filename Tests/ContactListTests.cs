using ContactListTests.Pages;
using NUnit.Framework;

namespace ContactListTests.Tests;

public class ContactListTests : BaseTest
{
    private LoginPage _loginPage;
    private SignUpPage _signUpPage;
    private ContactsPage _contactsPage;
    private AddContactPage _addContactPage;
    private ContactDetailsPage _contactDetailsPage;
    
    [SetUp]
    public new async Task SetUp()
    {
        await base.SetUp();
        _loginPage = new LoginPage(Page);
        _signUpPage = new SignUpPage(Page);
        _contactsPage = new ContactsPage(Page);
        _addContactPage = new AddContactPage(Page);
        _contactDetailsPage = new ContactDetailsPage(Page);
    }
    
    [Test]
    public async Task SignUpAddContactAndValidate()
    {
        // Generate unique email
        string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        string email = $"test{timestamp}@example.com";
        string firstName = "John";
        string lastName = "Doe";
        string password = "Password123!";
        
        // Sign up
        await _loginPage.GoToSignUpPage();
        await _signUpPage.SignUp(firstName, lastName, email, password);
        
        // Verify logged in
        Assert.IsTrue(await _contactsPage.IsLoggedIn(), "User should be logged in after sign up");
        
        // Add contact
        await _contactsPage.GoToAddContactPage();
        string contactFirstName = "Jane";
        string contactLastName = "Smith";
        string contactEmail = "<EMAIL>";
        string contactPhone = "1234567890";
        
        await _addContactPage.AddContact(contactFirstName, contactLastName, contactEmail, contactPhone);
        
        // Verify contact in list
        string fullName = $"{contactFirstName} {contactLastName}";
        Assert.IsTrue(await _contactsPage.IsContactInList(fullName), "Contact should be in the list");
        
        // Open contact details and validate
        await _contactsPage.OpenContactDetails(fullName);
        Assert.AreEqual(fullName, await _contactDetailsPage.GetContactName(), "Contact name should match");
        Assert.AreEqual(contactEmail, await _contactDetailsPage.GetContactEmail(), "Contact email should match");
        Assert.AreEqual(contactPhone, await _contactDetailsPage.GetContactPhone(), "Contact phone should match");
    }
    
    [Test]
    public async Task AddContactWithInvalidBirthdate()
    {
        // Sign up with a new user
        string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        string email = $"test{timestamp}@example.com";
        string firstName = "John";
        string lastName = "Doe";
        string password = "Password123!";
        
        await _loginPage.GoToSignUpPage();
        await _signUpPage.SignUp(firstName, lastName, email, password);
        
        // Add contact with invalid birthdate
        await _contactsPage.GoToAddContactPage();
        string contactFirstName = "Invalid";
        string contactLastName = "Birthdate";
        string contactEmail = "<EMAIL>";
        string contactPhone = "1234567890";
        string invalidBirthdate = "13/13/2023"; // Invalid month and day
        
        await _addContactPage.AddContact(contactFirstName, contactLastName, contactEmail, contactPhone, invalidBirthdate);
        
        // Verify error message
        string errorMessage = await _addContactPage.GetErrorMessage();
        Assert.IsTrue(errorMessage.Contains("Invalid date format"), "Error message should indicate invalid date format");
    }
    
    [Test]
    public async Task DeleteExistingContact()
    {
        // Sign up with a new user
        string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        string email = $"test{timestamp}@example.com";
        string firstName = "John";
        string lastName = "Doe";
        string password = "Password123!";
        
        await _loginPage.GoToSignUpPage();
        await _signUpPage.SignUp(firstName, lastName, email, password);
        
        // Add contact
        await _contactsPage.GoToAddContactPage();
        string contactFirstName = "Delete";
        string contactLastName = "Me";
        string contactEmail = "<EMAIL>";
        string contactPhone = "1234567890";
        
        await _addContactPage.AddContact(contactFirstName, contactLastName, contactEmail, contactPhone);
        
        // Verify contact in list
        string fullName = $"{contactFirstName} {contactLastName}";
        Assert.IsTrue(await _contactsPage.IsContactInList(fullName), "Contact should be in the list");
        
        // Delete contact
        await _contactsPage.OpenContactDetails(fullName);
        await _contactDetailsPage.DeleteContact();
        
        // Verify contact is no longer in the list
        Assert.IsFalse(await _contactsPage.IsContactInList(fullName), "Contact should not be in the list after deletion");
    }
}