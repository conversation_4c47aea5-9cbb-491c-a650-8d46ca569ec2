Write-Host "Setting up Playwright test framework..." -ForegroundColor Green

# Restore NuGet packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

# Build the project
Write-Host "Building the project..." -ForegroundColor Yellow
dotnet build

# Install browsers using the built-in method
Write-Host "Installing Playwright browsers..." -ForegroundColor Yellow
dotnet exec microsoft.playwright.cli install --with-deps

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Setup complete! You can now run tests with: dotnet test" -ForegroundColor Green
} else {
    Write-Host "❌ Browser installation failed. Please check the error messages above." -ForegroundColor Red
    exit 1
}
