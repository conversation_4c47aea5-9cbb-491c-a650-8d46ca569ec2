#!/bin/bash

echo "Setting up Playwright test framework..."

# Restore NuGet packages
echo "Restoring NuGet packages..."
dotnet restore

# Build the project
echo "Building the project..."
dotnet build

# Install browsers using the built-in method
echo "Installing Playwright browsers..."
dotnet exec microsoft.playwright.cli install --with-deps

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Setup complete! You can now run tests with: dotnet test"
else
    echo "❌ Browser installation failed. Please check the error messages above."
    exit 1
fi
