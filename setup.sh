#!/bin/bash

echo "Setting up Playwright test framework..."

# Restore NuGet packages
echo "Restoring NuGet packages..."
dotnet restore

# Build the project
echo "Building the project..."
dotnet build

# Install Playwright CLI tool globally
echo "Installing Playwright CLI tool..."
dotnet tool install --global Microsoft.Playwright.CLI

# Install browsers
echo "Installing Playwright browsers..."
playwright install

# Alternative method if global tool doesn't work
if [ $? -ne 0 ]; then
    echo "Global tool installation failed, trying alternative method..."
    dotnet exec microsoft.playwright.cli install
fi

echo "Setup complete! You can now run tests with: dotnet test"
