using Microsoft.Playwright;

namespace ContactListTests.Pages;

public class LoginPage
{
    private readonly IPage _page;
    
    private ILocator EmailInput => _page.Locator("#email");
    private ILocator PasswordInput => _page.Locator("#password");
    private ILocator SignUpButton => _page.GetByText("Sign up");
    private ILocator LoginButton => _page.GetByRole(AriaRole.Button, new() { Name = "Login" });
    
    public LoginPage(IPage page)
    {
        _page = page;
    }
    
    public async Task GoToSignUpPage()
    {
        await SignUpButton.ClickAsync();
    }
    
    public async Task Login(string email, string password)
    {
        await EmailInput.FillAsync(email);
        await PasswordInput.FillAsync(password);
        await LoginButton.ClickAsync();
    }
}