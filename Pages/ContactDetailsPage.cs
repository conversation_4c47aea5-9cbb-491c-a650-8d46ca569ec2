using Microsoft.Playwright;

namespace ContactListTests.Pages;

public class ContactDetailsPage
{
    private readonly IPage _page;
    
    private ILocator ContactName => _page.Locator("#contactName");
    private ILocator ContactEmail => _page.Locator("#contactEmail");
    private ILocator ContactPhone => _page.Locator("#contactPhone");
    private ILocator DeleteButton => _page.GetByRole(AriaRole.Button, new() { Name = "Delete Contact" });
    
    public ContactDetailsPage(IPage page)
    {
        _page = page;
    }
    
    public async Task<string> GetContactName()
    {
        return await ContactName.TextContentAsync();
    }
    
    public async Task<string> GetContactEmail()
    {
        return await ContactEmail.TextContentAsync();
    }
    
    public async Task<string> GetContactPhone()
    {
        return await ContactPhone.TextContentAsync();
    }
    
    public async Task DeleteContact()
    {
        await DeleteButton.ClickAsync();
        await _page.GetByRole(AriaRole.Button, new() { Name = "Delete" }).ClickAsync();
    }
}