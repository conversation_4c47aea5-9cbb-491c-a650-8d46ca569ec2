using Microsoft.Playwright;

namespace ContactListTests.Pages;

public class AddContactPage
{
    private readonly IPage _page;
    
    private ILocator FirstNameInput => _page.Locator("#firstName");
    private ILocator LastNameInput => _page.Locator("#lastName");
    private ILocator EmailInput => _page.Locator("#email");
    private ILocator PhoneInput => _page.Locator("#phone");
    private ILocator StreetAddress1Input => _page.Locator("#street1");
    private ILocator StreetAddress2Input => _page.Locator("#street2");
    private ILocator CityInput => _page.Locator("#city");
    private ILocator StateInput => _page.Locator("#stateProvince");
    private ILocator PostalCodeInput => _page.Locator("#postalCode");
    private ILocator CountryInput => _page.Locator("#country");
    private ILocator BirthdateInput => _page.Locator("#birthdate");
    private ILocator SubmitButton => _page.GetByRole(AriaRole.Button, new() { Name = "Submit" });
    
    public AddContactPage(IPage page)
    {
        _page = page;
    }
    
    public async Task AddContact(string firstName, string lastName, string email, string phone, string birthdate = "")
    {
        await FirstNameInput.FillAsync(firstName);
        await LastNameInput.FillAsync(lastName);
        await EmailInput.FillAsync(email);
        await PhoneInput.FillAsync(phone);
        if (!string.IsNullOrEmpty(birthdate))
        {
            await BirthdateInput.FillAsync(birthdate);
        }
        await SubmitButton.ClickAsync();
    }
    
    public async Task<string> GetErrorMessage()
    {
        return await _page.Locator(".error-message").TextContentAsync();
    }
}