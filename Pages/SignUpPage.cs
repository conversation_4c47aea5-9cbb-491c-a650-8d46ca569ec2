using Microsoft.Playwright;

namespace ContactListTests.Pages;

public class SignUpPage
{
    private readonly IPage _page;
    
    private ILocator FirstNameInput => _page.Locator("#firstName");
    private ILocator LastNameInput => _page.Locator("#lastName");
    private ILocator EmailInput => _page.Locator("#email");
    private ILocator PasswordInput => _page.Locator("#password");
    private ILocator SubmitButton => _page.GetByRole(AriaRole.Button, new() { Name = "Submit" });
    
    public SignUpPage(IPage page)
    {
        _page = page;
    }
    
    public async Task SignUp(string firstName, string lastName, string email, string password)
    {
        await FirstNameInput.FillAsync(firstName);
        await LastNameInput.FillAsync(lastName);
        await EmailInput.FillAsync(email);
        await PasswordInput.FillAsync(password);
        await SubmitButton.ClickAsync();
    }
}