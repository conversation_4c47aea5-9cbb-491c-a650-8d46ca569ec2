using Microsoft.Playwright;

namespace ContactListTests.Pages;

public class ContactsPage
{
    private readonly IPage _page;
    
    private ILocator AddContactButton => _page.GetByRole(AriaRole.Link, new() { Name = "Add a New Contact" });
    private ILocator ContactsTable => _page.Locator("#myTable");
    private ILocator LogoutButton => _page.GetByRole(AriaRole.Button, new() { Name = "Logout" });
    
    public ContactsPage(IPage page)
    {
        _page = page;
    }
    
    public async Task<bool> IsLoggedIn()
    {
        return await LogoutButton.IsVisibleAsync();
    }
    
    public async Task GoToAddContactPage()
    {
        await AddContactButton.ClickAsync();
    }
    
    public async Task OpenContactDetails(string contactName)
    {
        await _page.GetByRole(AriaRole.Cell, new() { Name = contactName }).ClickAsync();
    }
    
    public async Task<bool> IsContactInList(string contactName)
    {
        return await _page.GetByRole(AriaRole.Cell, new() { Name = contactName }).IsVisibleAsync();
    }
}