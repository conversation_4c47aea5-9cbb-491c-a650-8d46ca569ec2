name: Playwright Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 6.0.x
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Install Playwright
      run: dotnet tool install --global Microsoft.Playwright.CLI
    
    - name: Build
      run: dotnet build --no-restore
    
    - name: Install Playwright Browsers
      run: playwright install --with-deps
    
    - name: Run tests
      run: dotnet test --no-build --verbosity normal