{"format": 1, "restore": {"/Users/<USER>/Documents/augment-projects/vendi-machine-test/ContactListTests.csproj": {}}, "projects": {"/Users/<USER>/Documents/augment-projects/vendi-machine-test/ContactListTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/augment-projects/vendi-machine-test/ContactListTests.csproj", "projectName": "ContactListTests", "projectPath": "/Users/<USER>/Documents/augment-projects/vendi-machine-test/ContactListTests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/augment-projects/vendi-machine-test/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "Microsoft.Playwright.NUnit": {"target": "Package", "version": "[1.40.0, )"}, "NUnit": {"target": "Package", "version": "[3.13.3, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.4.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/RuntimeIdentifierGraph.json"}}}}}