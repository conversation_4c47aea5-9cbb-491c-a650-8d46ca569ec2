{"version": 2, "dgSpecHash": "NLoYe5r0IAc=", "success": true, "projectFilePath": "/Users/<USER>/Documents/augment-projects/vendi-machine-test/ContactListTests.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/6.0.0/microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.5.0/microsoft.codecoverage.17.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.5.0/microsoft.net.test.sdk.17.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.playwright/1.52.0/microsoft.playwright.1.52.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.playwright.nunit/1.40.0/microsoft.playwright.nunit.1.40.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.playwright.testadapter/1.40.0/microsoft.playwright.testadapter.1.40.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.5.0/microsoft.testplatform.objectmodel.17.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.5.0/microsoft.testplatform.testhost.17.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/netstandard.library/2.0.0/netstandard.library.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/nuget.frameworks/5.11.0/nuget.frameworks.5.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nunit/3.13.3/nunit.3.13.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/nunit3testadapter/4.4.2/nunit3testadapter.4.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/5.0.0/system.componentmodel.annotations.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/6.0.0/system.text.encodings.web.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/6.0.10/system.text.json.6.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.36/microsoft.aspnetcore.app.ref.6.0.36.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.36/microsoft.netcore.app.ref.6.0.36.nupkg.sha512"], "logs": []}